{"class_registry": {"UserService": {"fqcn": "AppOne.UserService", "package": "AppOne", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserService.java", "imports": [], "endpoints": [{"type": "GetMapping", "path": "/get/{id}", "method": "GET"}, {"type": "PostMapping", "path": "/create", "method": "POST"}, {"type": "RequestMapping", "path": "/user", "method": "GET"}], "db_entities": [], "source_code": "package AppOne;\n\nimport org.springframework.web.bind.annotation.*;\n\n@RestController\n@RequestMapping(\"/user\")\npublic class UserService {\n\n    private UserRepository userRepository = new UserRepository();\n\n    @PostMapping(\"/create\")\n    public User createUser(@RequestBody UserDto userDto) {\n        User user = UserUtils.convertDtoToEntity(userDto);\n        user.setStatus(\"ACTIVE\");\n        userRepository.save(user); // writes to User table\n        return user;\n    }\n\n    @GetMapping(\"/get/{id}\")\n    public User getUser(@PathVariable int id) {\n        return userRepository.findById(id); // reads from User table\n    }\n}"}, "UserUtils": {"fqcn": "AppOne.UserUtils", "package": "AppOne", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserUtils.java", "imports": [], "endpoints": [], "db_entities": [], "source_code": "package AppOne;\n\npublic class UserUtils {\n\n    public static User convertDtoToEntity(UserDto dto) {\n        User user = new User();\n        user.setName(dto.getName()); // dto.name FLOWS_TO user.name\n        user.setEmail(dto.getEmail()); // dto.email FLOWS_TO user.email\n        return user;\n    }\n\n    public static String generateUserCode(User user) {\n        String base = user.getName() + \"-\" + user.getEmail(); // user.name + user.email FLOWS_TO base\n        String code = base.toUpperCase(); // base TRANSFORMS_TO code\n        return code; // code PRODUCES output\n    }\n}"}, "OrderService": {"fqcn": "AppTwo.OrderService", "package": "AppTwo", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\OrderService.java", "imports": ["AppOne.User"], "endpoints": [{"type": "PostMapping", "path": "/create", "method": "POST"}, {"type": "RequestMapping", "path": "/order", "method": "GET"}], "db_entities": [], "source_code": "package AppTwo;\n\nimport org.springframework.web.bind.annotation.*;\nimport AppOne.User;\n\n@RestController\n@RequestMapping(\"/order\")\npublic class OrderService {\n\n    private OrderRepository orderRepository = new OrderRepository();\n\n    @PostMapping(\"/create\")\n    public Order createOrder(@RequestBody OrderDto orderDto) {\n        Order order = OrderUtils.convertDtoToEntity(orderDto);\n        order.setStatus(\"PENDING\");\n        orderRepository.save(order); // writes to Order table\n\n        User user = orderDto.getUser(); // retrieves User DTO reference\n        String userCode = AppOne.UserUtils.generateUserCode(user); // calls AppOne utility (cross-app)\n        order.setUserCode(userCode); // userCode FLOWS_TO order.userCode\n        return order;\n    }\n}"}, "OrderUtils": {"fqcn": "AppTwo.OrderUtils", "package": "AppTwo", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\OrderUtils.java", "imports": [], "endpoints": [], "db_entities": [], "source_code": "package AppTwo;\n\npublic class OrderUtils {\n\n    public static Order convertDtoToEntity(OrderDto dto) {\n        Order order = new Order();\n        order.setItem(dto.getItem()); // dto.item FLOWS_TO order.item\n        order.setQuantity(dto.getQuantity()); // dto.quantity FLOWS_TO order.quantity\n        return order;\n    }\n\n    public static double calculateTotal(double price, int quantity) {\n        double total = price * quantity; // price, quantity FLOWS_TO total\n        double taxedTotal = total * 1.18; // total TRANSFORMS_TO taxedTotal\n        return taxedTotal; // taxedTotal PRODUCES output\n    }\n}"}}, "dto_mappings": {}, "validated_edges": ["Userservice-DECLARES-Getuser", "OrderUtils-DECLARES-convertDtoToEntity", "Orderservice-DECLARES-Post /Create", "Getuser-READS_FROM-User", "Orderutils-DECLARES-Convertdtotoentity", "Createorder-USES-Order", "Calculatetotal-USES-Taxedtotal", "UserService-HAS_FIELD-Userrepository", "createOrder-USES-User", "OrderService-HAS_FIELD-Orderrepository", "convertDtoToEntity-USES-User", "AppOne-CONTAINS-UserService", "calculateTotal-USES-Taxedtotal", "Convertdtotoentity-USES-User", "OrderService-DECLARES-createOrder", "Calculatetotal-USES-Total", "OrderService-DECLARES-OrderService", "Generateusercode-USES-Base", "getUser-READS_FROM-User", "UserUtils-DECLARES-generateUserCode", "Generateusercode-USES-Code", "convertDtoToEntity-USES-Order", "AppTwo-CONTAINS-OrderUtils", "Userservice-HAS_FIELD-Userrepository", "Createuser-WRITES_TO-User", "Userutils-DECLARES-Convertdtotoentity", "AppOne-CONTAINS-UserUtils", "createOrder-USES-Usercode", "LineageTestProject-CONTAINS-AppTwo", "createUser-USES-User", "UserService-DECLARES-Post /Create", "Userservice-DECLARES-Createuser", "Userservice-DECLARES-Get /User", "Userservice-DECLARES-Get /Get/{Id}", "UserService-DECLARES-getUser", "Createorder-USES-Usercode", "createOrder-USES-Order", "OrderUtils-DECLARES-calculateTotal", "Orderutils-DECLARES-Calculatetotal", "OrderService-DECLARES-Get /Order", "UserService-DECLARES-createUser", "Userservice-DECLARES-Post /Create", "UserUtils-DECLARES-convertDtoToEntity", "Createorder-USES-User", "LineageTestProject-CONTAINS-AppOne", "UserService-DECLARES-UserService", "Createuser-USES-User", "createUser-WRITES_TO-User", "calculateTotal-USES-Total", "Convertdtotoentity-USES-Order", "Orderservice-DECLARES-Createorder", "UserService-DECLARES-Get /User", "generateUserCode-USES-Base", "OrderUtils-DECLARES-OrderUtils", "UserService-DECLARES-Get /Get/{Id}", "AppTwo-CONTAINS-OrderService", "UserUtils-DECLARES-UserUtils", "Userutils-DECLARES-Generateusercode", "OrderService-DECLARES-Post /Create", "generateUserCode-USES-Code", "Orderservice-HAS_FIELD-Orderrepository"], "code_index": {"UserService": {"methods": ["CreateUser", "GetUser"], "variables": [["package", "AppOne"], ["UserRepository", "userRepository"], ["User", "user"], ["return", "user"]], "annotations": ["RestController", "RequestMapping", "PostMapping", "RequestBody", "GetMapping", "PathVariable"]}, "UserUtils": {"methods": [], "variables": [["package", "AppOne"], ["User", "user"], ["return", "user"], ["String", "base"], ["String", "code"], ["return", "code"]], "annotations": []}, "OrderService": {"methods": ["CreateOrder"], "variables": [["package", "AppTwo"], ["OrderRepository", "orderRepository"], ["Order", "order"], ["User", "user"], ["String", "userCode"], ["return", "order"]], "annotations": ["RestController", "RequestMapping", "PostMapping", "RequestBody"]}, "OrderUtils": {"methods": [], "variables": [["package", "AppTwo"], ["Order", "order"], ["return", "order"], ["double", "total"], ["double", "taxedTotal"], ["return", "taxedTotal"]], "annotations": []}}, "variable_flows": {}, "method_signatures": {"Createuser": {"class": "UserService", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserService.java", "stage": "stage_3_registry"}, "Getuser": {"class": "UserService", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserService.java", "stage": "stage_3_registry"}, "Createorder": {"class": "OrderService", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\OrderService.java", "stage": "stage_3_registry"}, "CreateUser": {"class": "UserService", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserService.java", "stage": "stage_3_registry"}, "GetUser": {"class": "UserService", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppOne\\UserService.java", "stage": "stage_3_registry"}, "CreateOrder": {"class": "OrderService", "file_path": "C:\\Shaik\\sample\\LineageTestProject\\AppTwo\\OrderService.java", "stage": "stage_3_registry"}}, "transformation_cache": {}, "variable_contexts": {}, "stage_2_results": {"relationships": 6, "folders": 2, "files": 4, "file_class_relationships": 4}, "ast_relationships": 28, "ast_name_mapping": {"userservice": "UserService", "createuser": "createUser", "getuser": "getUser", "userutils": "UserUtils", "convertdtotoentity": "convertDtoToEntity", "generateusercode": "generateUserCode", "orderservice": "OrderService", "createorder": "createOrder", "orderutils": "OrderUtils", "calculatetotal": "calculateTotal"}}