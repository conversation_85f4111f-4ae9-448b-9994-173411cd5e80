source_node,source_type,destination_node,destination_type,relationship
LineageTestProject,Folder,AppOne,Folder,CONTAINS
AppOne,Folder,UserService,File,CONTAINS
AppOne,Folder,UserUtils,File,CONTAINS
LineageTestProject,Folder,AppTwo,Folder,CONTAINS
AppTwo,Folder,OrderService,File,CONTAINS
AppTwo,Folder,OrderUtils,File,CONTAINS
UserService,File,UserService,Class,DECLARES
UserUtils,File,UserUtils,Class,DECLARES
OrderService,File,OrderService,Class,DECLARES
OrderUtils,File,OrderUtils,Class,DECLARES
UserService,Class,Get /Get/{Id},Endpoint,DECLARES
UserService,Class,Post /Create,Endpoint,DECLARES
UserService,Class,Get /User,Endpoint,DECLARES
UserService,Class,Userrepository,Variable,HAS_FIELD
UserService,Class,CreateUser,Method,DECLARES
CreateUser,Method,User,Variable,USES
UserService,Class,GetUser,Method,DECLARES
CreateUser,Method,User,Table,WRITES_TO
GetUser,Method,User,Table,READS_FROM
UserUtils,Class,ConvertDtoToEntity,Method,DECLARES
ConvertDtoToEntity,Method,User,Variable,USES
UserUtils,Class,GenerateUserCode,Method,DECLARES
GenerateUserCode,Method,Base,Variable,USES
GenerateUserCode,Method,Code,Variable,USES
OrderService,Class,Post /Create,Endpoint,DECLARES
OrderService,Class,Get /Order,Endpoint,DECLARES
OrderService,Class,Orderrepository,Variable,HAS_FIELD
OrderService,Class,CreateOrder,Method,DECLARES
CreateOrder,Method,Order,Variable,USES
CreateOrder,Method,User,Variable,USES
CreateOrder,Method,Usercode,Variable,USES
OrderUtils,Class,ConvertDtoToEntity,Method,DECLARES
ConvertDtoToEntity,Method,Order,Variable,USES
OrderUtils,Class,CalculateTotal,Method,DECLARES
CalculateTotal,Method,Total,Variable,USES
CalculateTotal,Method,Taxedtotal,Variable,USES
Userdto,Variable,User,Variable,TRANSFORMS_TO
Userdto,Variable,CreateUser,Method,FLOWS_TO
UserService,Class,Post:/User/Create,Endpoint,EXPOSES
UserService,Class,Get:/User/Get/{Id},Endpoint,EXPOSES
CreateUser,Method,Post:/User/Create,Endpoint,MAPS_TO
GetUser,Method,Get:/User/Get/{Id},Endpoint,MAPS_TO
Post:/User/Create,Endpoint,Userdto,Variable,ACCEPTS
Post:/User/Create,Endpoint,User,Variable,RETURNS
Get:/User/Get/{Id},Endpoint,Id,Variable,ACCEPTS
Get:/User/Get/{Id},Endpoint,User,Variable,RETURNS
CreateUser,Method,ConvertDtoToEntity,Method,CALLS
CreateUser,Method,Save,Method,CALLS
GetUser,Method,Findbyid,Method,CALLS
Name,Variable,Base,Variable,FLOWS_TO
Email,Variable,Base,Variable,FLOWS_TO
Base,Variable,Code,Variable,TRANSFORMS_TO
Code,Variable,Output,Variable,PRODUCES
Orderdto,Variable,Order,Variable,TRANSFORMS_TO
Orderstatus (Pending),Variable,Status,Variable,FLOWS_TO
User,Variable,Usercode,Variable,TRANSFORMS_TO
CreateOrder,Method,Order,Table,WRITES_TO
OrderService,Class,Post:/Order/Create,Endpoint,EXPOSES
CreateOrder,Method,Post:/Order/Create,Endpoint,MAPS_TO
Post:/Order/Create,Endpoint,Orderdto,Variable,ACCEPTS
Post:/Order/Create,Endpoint,Order,Variable,RETURNS
CreateOrder,Method,ConvertDtoToEntity,Method,CALLS
CreateOrder,Method,Setstatus,Method,CALLS
CreateOrder,Method,Save,Method,CALLS
CreateOrder,Method,GetUser,Method,CALLS
CreateOrder,Method,GenerateUserCode,Method,CALLS
CreateOrder,Method,Setusercode,Method,CALLS
CreateOrder,Method,Appone.Userutils.Generateusercode,Externalservice,INVOKES
Price,Variable,Total,Variable,FLOWS_TO
Quantity,Variable,Total,Variable,FLOWS_TO
Total,Variable,Taxedtotal,Variable,TRANSFORMS_TO
Taxedtotal,Variable,Output,Variable,PRODUCES
ConvertDtoToEntity,Method,Order,Variable,PRODUCES
CalculateTotal,Method,Output,Variable,PRODUCES
