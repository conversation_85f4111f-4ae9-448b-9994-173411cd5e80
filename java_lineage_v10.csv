source_node,source_type,destination_node,destination_type,relationship
LineageTestProject,Folder,AppOne,Folder,CONTAINS
AppOne,Folder,UserService,File,CONTAINS
AppOne,Folder,UserUtils,File,CONTAINS
LineageTestProject,Folder,AppTwo,Folder,CONTAINS
AppTwo,Folder,OrderService,File,CONTAINS
AppTwo,Folder,OrderUtils,File,CONTAINS
UserService,File,UserService,Class,DECLARES
UserUtils,File,UserUtils,Class,DECLARES
OrderService,File,OrderService,Class,DECLARES
OrderUtils,File,OrderUtils,Class,DECLARES
Userservice,Class,Get /Get/{Id},Endpoint,DECLARES
Userservice,Class,Post /Create,Endpoint,DECLARES
Userservice,Class,Get /User,Endpoint,DECLARES
Userservice,Class,Userrepository,Variable,HAS_FIELD
Userservice,Class,Createuser,Method,DECLAR<PERSON>
Createuser,Method,User,Variable,USES
Userservice,Class,Getuser,Method,DECLARES
Createuser,Method,User,Table,WRITES_TO
Getuser,Method,User,Table,READS_FROM
Userutils,Class,Convertdtotoentity,Method,DECLARES
Convertdtotoentity,Method,User,Variable,USES
Userutils,Class,Generateusercode,Method,DECLARES
Generateusercode,Method,Base,Variable,USES
Generateusercode,Method,Code,Variable,USES
Orderservice,Class,Createorder,Method,DECLARES
Orderservice,Class,Orderrepository,Variable,HAS_FIELD
Orderservice,Class,Post /Create,Endpoint,DECLARES
Createorder,Method,Order,Variable,USES
Createorder,Method,User,Variable,USES
Createorder,Method,Usercode,Variable,USES
Orderutils,Class,Convertdtotoentity,Method,DECLARES
Convertdtotoentity,Method,Order,Variable,USES
Orderutils,Class,Calculatetotal,Method,DECLARES
Calculatetotal,Method,Total,Variable,USES
Calculatetotal,Method,Taxedtotal,Variable,USES
Userdto,Variable,User,Variable,TRANSFORMS_TO
User,Variable,Save,Method,FLOWS_TO
Userservice,Class,Post:/User/Create,Endpoint,EXPOSES
Userservice,Class,Get:/User/Get/{Id},Endpoint,EXPOSES
Createuser,Method,Post:/User/Create,Endpoint,MAPS_TO
Post:/User/Create,Endpoint,Userdto,Variable,ACCEPTS
Post:/User/Create,Endpoint,User,Variable,RETURNS
Getuser,Method,Get:/User/Get/{Id},Endpoint,MAPS_TO
Get:/User/Get/{Id},Endpoint,Id,Variable,ACCEPTS
Get:/User/Get/{Id},Endpoint,User,Variable,RETURNS
Createuser,Method,Convertdtotoentity,Method,CALLS
Createuser,Method,Save,Method,CALLS
Getuser,Method,Findbyid,Method,CALLS
Convertdtotoentity,Method,User,Variable,PRODUCES
Name,Variable,Base,Variable,FLOWS_TO
Email,Variable,Base,Variable,FLOWS_TO
Base,Variable,Code,Variable,TRANSFORMS_TO
Generateusercode,Method,Code,Variable,PRODUCES
Orderdto,Variable,Order,Variable,TRANSFORMS_TO
Pending,Variable,Status,Variable,FLOWS_TO
User,Variable,Generateusercode(User),Variable,FLOWS_TO
Createorder,Method,Order,Table,WRITES_TO
Orderservice,Class,Post:/Order/Create,Endpoint,EXPOSES
Createorder,Method,Post:/Order/Create,Endpoint,MAPS_TO
Post:/Order/Create,Endpoint,Orderdto,Variable,ACCEPTS
Post:/Order/Create,Endpoint,Order,Variable,RETURNS
Createorder,Method,Convertdtotoentity,Method,CALLS
Createorder,Method,Setstatus,Method,CALLS
Createorder,Method,Save,Method,CALLS
Createorder,Method,Getuser,Method,CALLS
Createorder,Method,Appone.Userutils.Generateusercode,Externalservice,CALLS
Createorder,Method,Setusercode,Method,CALLS
Double:Price,Variable,Double:Total,Variable,FLOWS_TO
Int:Quantity,Variable,Double:Total,Variable,FLOWS_TO
Double:Total,Variable,Double:Taxedtotal,Variable,TRANSFORMS_TO
Orderutils:convertdtotoentity,Method,Order:Order,Variable,PRODUCES
Orderutils:calculatetotal,Method,Double:Taxedtotal,Variable,PRODUCES
