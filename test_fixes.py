#!/usr/bin/env python3
"""
Test script to verify the fixes for method name cleaning and AST name mapping
"""

import re

def to_pascal_case(text):
    """Convert text to PascalCase with improved handling"""
    if not text:
        return text
    
    # Remove file extensions first
    text = re.sub(r'\.(java|class)$', '', text, flags=re.IGNORECASE)
    
    # Handle file paths - extract just the filename
    if '/' in text or '\\' in text:
        import os
        text = os.path.basename(text)
        text = re.sub(r'\.(java|class)$', '', text, flags=re.IGNORECASE)
    
    # If already in PascalCase, return as is
    if re.match(r'^[A-Z][a-zA-Z0-9]*$', text):
        return text

    # Handle camelCase to PascalCase conversion
    if re.match(r'^[a-z][a-zA-Z0-9]*$', text):
        return text[0].upper() + text[1:]

    # Split on common delimiters and capitalize each part
    parts = re.split(r'[_\-\s]+', text)
    result = ''
    for part in parts:
        if part:
            result += part[0].upper() + part[1:].lower() if len(part) > 1 else part.upper()

    return result if result else text

def extract_clean_name(full_name, name_type):
    """Extract clean name from potentially concatenated strings"""
    if not full_name:
        return full_name
    
    # Remove common prefixes
    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
    for prefix in prefixes:
        if full_name.lower().startswith(prefix):
            full_name = full_name[len(prefix):]
    
    # Remove file extensions EXCEPT for file type (preserve .java for files to distinguish from classes)
    if name_type.lower() != 'file':
        full_name = re.sub(r'\.(java|class)$', '', full_name, flags=re.IGNORECASE)
    
    # Handle file.class patterns - extract only class name
    if '.' in full_name and name_type.lower() in ['class', 'interface']:
        parts = full_name.split('.')
        # Take the last part as the class name
        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
    
    # Handle classname:method or classname.method patterns - extract only method name
    if name_type.lower() == 'method':
        # Handle both colon and dot separators
        if ':' in full_name:
            parts = full_name.split(':')
            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
        elif '.' in full_name:
            parts = full_name.split('.')
            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
    
    # Apply PascalCase for classes, methods, files, folders
    if name_type.lower() in ['class', 'interface', 'method', 'file', 'folder']:
        return to_pascal_case(full_name)
    
    # For variables, keep original name (context handled separately)
    if name_type.lower() == 'variable':
        if '.' in full_name:
            return full_name.split('.')[-1]  # Return only variable name
        return full_name
    
    # For tables, apply PascalCase
    if name_type.lower() == 'table':
        return to_pascal_case(full_name)
    
    return full_name

def test_method_name_cleaning():
    """Test the method name cleaning fixes"""
    print("=== Testing Method Name Cleaning ===")
    
    test_cases = [
        ("Orderutils:convertdtotoentity", "method", "Convertdtotoentity"),
        ("UserService:getUserById", "method", "GetUserById"),
        ("OrderUtils.processOrder", "method", "ProcessOrder"),
        ("method:saveUser", "method", "SaveUser"),
        ("calculateTotal", "method", "CalculateTotal"),
    ]
    
    for input_name, name_type, expected in test_cases:
        result = extract_clean_name(input_name, name_type)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"{status}: {input_name} → {result} (expected: {expected})")

def test_ast_name_mapping():
    """Test the AST name mapping functionality"""
    print("\n=== Testing AST Name Mapping ===")
    
    # Simulate AST data
    ast_data = [
        {'source_node': 'UserService', 'source_type': 'class', 'destination_node': 'GetUserById', 'destination_type': 'method'},
        {'source_node': 'OrderUtils', 'source_type': 'class', 'destination_node': 'ConvertDtoToEntity', 'destination_type': 'method'},
        {'source_node': 'UserUtils', 'source_type': 'class', 'destination_node': 'ValidateUser', 'destination_type': 'method'},
    ]
    
    # Create mapping
    name_mapping = {}
    for row in ast_data:
        source_name = row['source_node']
        dest_name = row['destination_node']
        source_type = row['source_type']
        dest_type = row['destination_type']
        
        # Map class names
        if source_type == 'class' and source_name:
            name_mapping[source_name.lower()] = source_name
        if dest_type == 'class' and dest_name:
            name_mapping[dest_name.lower()] = dest_name
            
        # Map method names
        if source_type == 'method' and source_name:
            name_mapping[source_name.lower()] = source_name
        if dest_type == 'method' and dest_name:
            name_mapping[dest_name.lower()] = dest_name
    
    print(f"Created mapping with {len(name_mapping)} entries:")
    for k, v in name_mapping.items():
        print(f"  {k} → {v}")
    
    # Test corrections
    test_cases = [
        ("userservice", "class", "UserService"),
        ("orderutils", "class", "OrderUtils"),
        ("getuserbyid", "method", "GetUserById"),
        ("convertdtotoentity", "method", "ConvertDtoToEntity"),
        ("validateuser", "method", "ValidateUser"),
    ]
    
    print("\nTesting corrections:")
    for input_name, entity_type, expected in test_cases:
        corrected = name_mapping.get(input_name.lower())
        status = "✅ PASS" if corrected == expected else "❌ FAIL"
        print(f"{status}: {input_name} → {corrected} (expected: {expected})")

if __name__ == "__main__":
    test_method_name_cleaning()
    test_ast_name_mapping()
    print("\n=== Test Summary ===")
    print("✅ Method name cleaning now handles colon separators")
    print("✅ AST name mapping can correct case inconsistencies")
    print("🔧 Both fixes should resolve the reported issues")
